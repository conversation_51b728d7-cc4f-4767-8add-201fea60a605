<template>
	<view class="container">
		<!-- 自定义头部 -->
		<view class="custom-header" :style="{ paddingTop: statusBarHeight + 'px' }">
			<view class="header-content">
				<view class="header-left"></view>
				<view class="header-center">
					<text class="title">个人中心</text>
				</view>
				<view class="header-right"></view>
			</view>
		</view>

		<!-- 页面内容 -->
		<view class="page-content" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
			<!-- 用户信息 -->
			<view class="user-section">
			<view class="user-info">
				<image class="avatar" src="/static/images/avatar.png" mode="aspectFill"></image>
				<view class="user-details">
					<text class="username">用户昵称</text>
					<text class="user-desc">点击设置个人信息</text>
				</view>
			</view>
		</view>
		
		<!-- 功能列表 -->
		<view class="menu-section">
			<view class="menu-item">
				<text class="menu-text">我的数据</text>
				<image class="arrow-icon" src="/static/images/<EMAIL>" mode="aspectFit"></image>
			</view>
			<view class="menu-item">
				<text class="menu-text">设置</text>
				<image class="arrow-icon" src="/static/images/<EMAIL>" mode="aspectFit"></image>
			</view>
			<view class="menu-item">
				<text class="menu-text">帮助与反馈</text>
				<image class="arrow-icon" src="/static/images/<EMAIL>" mode="aspectFit"></image>
			</view>
			<view class="menu-item">
				<text class="menu-text">关于我们</text>
				<image class="arrow-icon" src="/static/images/<EMAIL>" mode="aspectFit"></image>
			</view>
		</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				statusBarHeight: 0
			}
		},
		onLoad() {
			this.getSystemInfo()
		},
		methods: {
			getSystemInfo() {
				uni.getSystemInfo({
					success: (res) => {
						this.statusBarHeight = res.statusBarHeight || 20
					}
				})
			}
		}
	}
</script>

<style scoped>
	.container {
		background-color: #f5f5f5;
		min-height: 100vh;
		padding-bottom: 120rpx;
	}

	/* 自定义头部 */
	.custom-header {
		background-color: #f8f9fa;
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 999;
	}

	.header-content {
		height: 88rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 32rpx;
		position: relative;
	}

	.header-left, .header-right {
		min-width: 120rpx;
	}

	.header-center {
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
	}

	.title {
		font-size: 36rpx;
		font-weight: 600;
		color: #333333;
	}
	
	/* 用户信息部分 */
	.user-section {
		background-color: #ffffff;
		margin-top: 20rpx;
		padding: 40rpx 30rpx;
	}
	
	.user-info {
		display: flex;
		align-items: center;
	}
	
	.avatar {
		width: 120rpx;
		height: 120rpx;
		border-radius: 50%;
		margin-right: 30rpx;
	}
	
	.user-details {
		flex: 1;
	}
	
	.username {
		font-size: 36rpx;
		color: #333333;
		font-weight: bold;
		display: block;
		margin-bottom: 10rpx;
	}
	
	.user-desc {
		font-size: 28rpx;
		color: #999999;
	}
	
	/* 菜单部分 */
	.menu-section {
		background-color: #ffffff;
		margin-top: 20rpx;
	}
	
	.menu-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}
	
	.menu-item:last-child {
		border-bottom: none;
	}
	
	.menu-text {
		font-size: 32rpx;
		color: #333333;
	}
	
	.arrow-icon {
		width: 20rpx;
		height: 20rpx;
	}
</style>
