<template>
  <view class="custom-header" :style="{ paddingTop: statusBarHeight + 'px' }">
    <view class="header-content">
      <!-- 左侧区域 -->
      <view class="header-left">
        <view v-if="showBack" class="back-btn" @click="goBack">
          <image src="/static/images/icon-back.png" class="back-icon" />
        </view>
        <view v-if="leftIcon" class="left-icon" @click="onLeftClick">
          <image :src="leftIcon" class="icon" />
        </view>
      </view>
      
      <!-- 中间标题区域 -->
      <view class="header-center">
        <text class="title">{{ title }}</text>
      </view>
      
      <!-- 右侧区域 -->
      <view class="header-right">
        <view v-if="rightIcon" class="right-icon" @click="onRightClick">
          <image :src="rightIcon" class="icon" />
        </view>
        <view v-if="rightText" class="right-text" @click="onRightClick">
          <text class="text">{{ rightText }}</text>
        </view>
        <view v-if="showMore" class="more-btn" @click="onMoreClick">
          <image src="/static/images/icon-more.png" class="more-icon" />
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'CustomHeader',
  props: {
    title: {
      type: String,
      default: ''
    },
    showBack: {
      type: Boolean,
      default: false
    },
    leftIcon: {
      type: String,
      default: ''
    },
    rightIcon: {
      type: String,
      default: ''
    },
    rightText: {
      type: String,
      default: ''
    },
    showMore: {
      type: Boolean,
      default: false
    },
    backgroundColor: {
      type: String,
      default: '#f8f9fa'
    }
  },
  data() {
    return {
      statusBarHeight: 0
    }
  },
  mounted() {
    this.getSystemInfo()
  },
  methods: {
    getSystemInfo() {
      uni.getSystemInfo({
        success: (res) => {
          this.statusBarHeight = res.statusBarHeight || 20
        }
      })
    },
    goBack() {
      uni.navigateBack()
    },
    onLeftClick() {
      this.$emit('leftClick')
    },
    onRightClick() {
      this.$emit('rightClick')
    },
    onMoreClick() {
      this.$emit('moreClick')
    }
  }
}
</script>

<style scoped>
.custom-header {
  background-color: #f8f9fa;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
}

.header-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  position: relative;
}

.header-left {
  display: flex;
  align-items: center;
  min-width: 60px;
}

.header-center {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.title {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
}

.header-right {
  display: flex;
  align-items: center;
  min-width: 60px;
  justify-content: flex-end;
}

.back-btn, .left-icon, .right-icon, .more-btn {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon, .icon, .more-icon {
  width: 20px;
  height: 20px;
}

.right-text {
  padding: 4px 8px;
}

.text {
  font-size: 14px;
  color: #00D4AA;
}
</style>
