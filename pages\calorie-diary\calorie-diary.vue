<template>
	<view class="container">
		<!-- 自定义头部 -->
		<view class="custom-header" :style="{ paddingTop: statusBarHeight + 'px' }">
			<view class="header-content">
				<view class="header-left">
					<image class="avatar" src="/static/images/<EMAIL>" mode="aspectFit"></image>
					<text class="greeting">联系客服</text>
				</view>
				<view class="header-center">
					<text class="title">热量日记</text>
				</view>
			</view>

			<!-- 记录提示 -->
			<view class="record-tip">
				<text class="tip-text">3秒记录+定制方案+精准控卡</text>
				<view class="start-btn">
					<image class="reminder-icon" src="/static/images/<EMAIL>" mode="aspectFit"></image>
					<text class="btn-text">开启提醒</text>
				</view>
			</view>
		</view>

		<!-- 页面内容 -->
		<view class="page-content">
		
		<!-- 今日热量剩余 -->
		<view class="calorie-section">
			<view class="section-header">
				<text class="section-title">今日热量剩余</text>
				<view class="tip-btn">
					<text class="tip-btn-text">没吃制热量方案</text>
				</view>
			</view>
			
			<view class="calorie-display">
				<view class="calorie-left">
					<text class="calorie-label">今日还可吃</text>
					<view class="calorie-number">
						<text class="number">640</text>
						<text class="unit">千卡</text>
					</view>
					<view class="progress-bar">
						<view class="progress-fill"></view>
					</view>
				</view>
				<view class="calorie-right">
					<image class="fruit-icon" src="/static/images/fruit.png" mode="aspectFit"></image>
				</view>
			</view>
			
			<!-- 热量统计 -->
			<view class="calorie-stats">
				<view class="stat-item">
					<text class="stat-label">每日热量计划</text>
					<text class="stat-value">1200</text>
					<text class="stat-unit">千卡</text>
				</view>
				<view class="stat-divider">-</view>
				<view class="stat-item">
					<text class="stat-label">已经吃了</text>
					<text class="stat-value">560</text>
					<text class="stat-unit">千卡</text>
				</view>
				<view class="stat-divider">+</view>
				<view class="stat-item">
					<text class="stat-label">运动消耗</text>
					<text class="stat-value">0</text>
					<text class="stat-unit">千卡</text>
				</view>
			</view>
		</view>
		
		<!-- 今日热量明细 -->
		<view class="detail-section">
			<text class="section-title">今日热量明细</text>
			<view class="detail-content">
				<text class="detail-text">像聊天一样记录热量和体重。</text>
				<text class="detail-text">比如：午餐吃了半碗米饭，下午吃了一小袋混合坚果，晚上跑步30分钟。</text>
				<text class="detail-text">今天体重99斤。</text>
				<text class="detail-text">也可以对食物进行拍照自动识别分析热量。</text>
			</view>
			
			<view class="action-buttons">
				<view class="action-btn photo-btn">
					<image class="btn-icon" src="/static/images/<EMAIL>" mode="aspectFit"></image>
					<text class="btn-text">拍照识别</text>
				</view>
				<view class="action-btn voice-btn">
					<image class="btn-icon" src="/static/images/<EMAIL>" mode="aspectFit"></image>
					<text class="btn-text">语音输入</text>
				</view>
			</view>
			
			<view class="record-btn">
				<text class="record-btn-text">热量分析&记录</text>
			</view>
		</view>
		
		<!-- 体重和步数 -->
		<view class="stats-section">
			<view class="stat-row">
				<view class="stat-card">
					<text class="stat-title">今日体重</text>
					<image class="stat-icon" src="/static/images/more-dots.png" mode="aspectFit"></image>
				</view>
				<view class="stat-card">
					<text class="stat-title">微信步数</text>
					<image class="stat-icon" src="/static/images/more-dots.png" mode="aspectFit"></image>
				</view>
			</view>
			
			<view class="weight-display">
				<text class="weight-number">62.4</text>
				<text class="weight-unit">kg</text>
				<image class="trend-icon" src="/static/images/trend-up.png" mode="aspectFit"></image>
			</view>
			
			<view class="steps-display">
				<text class="steps-number">——</text>
				<text class="steps-unit">步 未授权</text>
				<image class="phone-icon" src="/static/images/phone.png" mode="aspectFit"></image>
			</view>
		</view>
		
		<!-- 食物记录列表 -->
		<view class="food-list">
			<view class="food-item">
				<view class="food-info">
					<view class="food-icon orange"></view>
					<view class="food-details">
						<text class="food-name">牛奶</text>
						<text class="food-amount">300毫升</text>
						<view class="food-nutrition">
							<text class="nutrition-item">碳水 0.6g</text>
							<text class="nutrition-item">蛋白质 7.8g</text>
							<text class="nutrition-item">脂肪 6.2g</text>
						</view>
					</view>
				</view>
				<view class="food-calorie">
					<text class="calorie-text">186千卡</text>
					<image class="arrow-icon" src="/static/images/<EMAIL>" mode="aspectFit"></image>
				</view>
			</view>
			
			<view class="food-item">
				<view class="food-info">
					<view class="food-icon green"></view>
					<view class="food-details">
						<text class="food-name">骑行</text>
						<text class="food-amount">15分钟</text>
					</view>
				</view>
				<view class="food-calorie">
					<text class="calorie-text">30千卡</text>
					<image class="arrow-icon" src="/static/images/<EMAIL>" mode="aspectFit"></image>
				</view>
			</view>
			
			<view class="food-item">
				<view class="food-info">
					<view class="food-icon orange"></view>
					<view class="food-details">
						<text class="food-name">坚果</text>
						<text class="food-amount">60克</text>
						<view class="food-nutrition">
							<text class="nutrition-item">碳水 0.8g</text>
							<text class="nutrition-item">蛋白质 9.6g</text>
							<text class="nutrition-item">脂肪 1.2g</text>
						</view>
					</view>
				</view>
				<view class="food-calorie">
					<text class="calorie-text">696千卡</text>
					<image class="arrow-icon" src="/static/images/<EMAIL>" mode="aspectFit"></image>
				</view>
			</view>
		</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				statusBarHeight: 0
			}
		},
		onLoad() {
			this.getSystemInfo()
		},
		methods: {
			getSystemInfo() {
				uni.getSystemInfo({
					success: (res) => {
						this.statusBarHeight = res.statusBarHeight || 20
					}
				})
			}
		}
	}
</script>

<style scoped>
	.container {
		background-color: #f5f5f5;
		min-height: 100vh;
		padding-bottom: 120rpx;
	}

	/* 自定义头部 */
	.custom-header {
		background: linear-gradient(to bottom, #E3FFFA, #F2F2F2);
		position: static;
		top: 0;
		left: 0;
		right: 0;
		z-index: 999;
		padding-bottom: 30rpx;
	}

	.header-content {
		height: 120rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 0 30rpx;
		position: relative;
	}

	.header-left {
		display: flex;
		align-items: center;
		min-width: 140rpx;
		position: absolute;
		left: 30rpx;
		top: 104rpx;
		transform: translateY(-50%);
	}

	.avatar {
		width: 40rpx;
		height: 40rpx;
		border-radius: 50%;
		margin-right: 12rpx;
	}

	.greeting {
		font-size: 28rpx;
		color: #666666;
		font-weight: bold;
	}

	.header-center {
		position: absolute;
		left: 50%;
		top: 104rpx;
		transform: translate(-50%, -50%);
	}

	.title {
		font-size: 40rpx;
		font-weight: 600;
		color: #333333;
		letter-spacing: 1rpx;
		font-family: "PingFang SC-Bold", "Microsoft YaHei", sans-serif;
	}


	
	/* 页面内容 */
	.page-content {
		padding-top: 20rpx;
	}

	/* 记录提示 */
	.record-tip {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 30rpx 0 30rpx;
		margin-top: 42rpx;
	}
	
	.tip-text {
		font-size: 34rpx;
		color: #333333;
		font-weight: bold;
	}

	.start-btn {
		width: 164rpx;
		height: 52rpx;
		background: #FFFFFF;
		border-radius: 36rpx;
		border: 0rpx solid #FFFFFF;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	}

	.reminder-icon {
		width: 36rpx;
		height: 36rpx;
		margin-right: 8rpx;
	}

	.btn-text {
		font-size: 24rpx;
		color: #00D4AA;
		font-weight: 500;
		text-align: center;
	}
	
	/* 热量部分 */
	.calorie-section {
		background-color: #ffffff;
		margin: 0 20rpx 20rpx 20rpx;
		padding: 30rpx;
		border-radius: 16rpx;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
	}
	
	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 30rpx;
	}
	
	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
	}
	
	.tip-btn {
		background-color: #E8F8F5;
		padding: 8rpx 16rpx;
		border-radius: 16rpx;
	}
	
	.tip-btn-text {
		font-size: 24rpx;
		color: #00D4AA;
	}
	
	.calorie-display {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 40rpx;
	}
	
	.calorie-left {
		flex: 1;
	}
	
	.calorie-label {
		font-size: 28rpx;
		color: #666666;
	}
	
	.calorie-number {
		display: flex;
		align-items: baseline;
		margin: 20rpx 0;
	}
	
	.number {
		font-size: 80rpx;
		font-weight: bold;
		color: #00D4AA;
	}
	
	.unit {
		font-size: 28rpx;
		color: #666666;
		margin-left: 10rpx;
	}
	
	.progress-bar {
		width: 100%;
		height: 8rpx;
		background-color: #E5E5E5;
		border-radius: 4rpx;
		overflow: hidden;
	}
	
	.progress-fill {
		width: 53%;
		height: 100%;
		background-color: #00D4AA;
	}
	
	.calorie-right {
		margin-left: 40rpx;
	}
	
	.fruit-icon {
		width: 120rpx;
		height: 120rpx;
	}
	
	.calorie-stats {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	
	.stat-item {
		text-align: center;
		flex: 1;
	}
	
	.stat-label {
		display: block;
		font-size: 24rpx;
		color: #999999;
		margin-bottom: 10rpx;
	}
	
	.stat-value {
		font-size: 36rpx;
		font-weight: bold;
		color: #333333;
	}
	
	.stat-unit {
		font-size: 24rpx;
		color: #666666;
		margin-left: 5rpx;
	}
	
	.stat-divider {
		font-size: 36rpx;
		color: #999999;
		margin: 0 20rpx;
	}
	
	/* 明细部分 */
	.detail-section {
		background-color: #ffffff;
		margin: 0 20rpx 20rpx 20rpx;
		padding: 30rpx;
		border-radius: 16rpx;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
	}
	
	.detail-content {
		margin: 20rpx 0 30rpx 0;
	}
	
	.detail-text {
		display: block;
		font-size: 28rpx;
		color: #666666;
		line-height: 1.6;
		margin-bottom: 10rpx;
	}
	
	.action-buttons {
		display: flex;
		justify-content: space-around;
		margin-bottom: 30rpx;
	}
	
	.action-btn {
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.btn-icon {
		width: 60rpx;
		height: 60rpx;
		margin-bottom: 10rpx;
	}
	
	.record-btn {
		background-color: #00D4AA;
		padding: 24rpx;
		border-radius: 16rpx;
		text-align: center;
		box-shadow: 0 4rpx 16rpx rgba(0, 212, 170, 0.3);
	}
	
	.record-btn-text {
		font-size: 32rpx;
		color: #ffffff;
		font-weight: bold;
	}
	
	/* 统计部分 */
	.stats-section {
		display: flex;
		margin: 0 20rpx 20rpx 20rpx;
		gap: 20rpx;
	}
	
	.stat-row {
		display: flex;
		flex-direction: column;
		flex: 1;
	}
	
	.stat-card {
		background-color: #ffffff;
		padding: 20rpx;
		border-radius: 16rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
	}
	
	.stat-title {
		font-size: 28rpx;
		color: #333333;
	}
	
	.stat-icon {
		width: 30rpx;
		height: 30rpx;
	}
	
	.weight-display, .steps-display {
		background-color: #ffffff;
		padding: 30rpx 20rpx;
		border-radius: 16rpx;
		text-align: center;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
	}
	
	.weight-number, .steps-number {
		font-size: 48rpx;
		font-weight: bold;
		color: #333333;
	}
	
	.weight-unit, .steps-unit {
		font-size: 24rpx;
		color: #666666;
		margin-left: 10rpx;
	}
	
	.trend-icon, .phone-icon {
		width: 30rpx;
		height: 30rpx;
		margin-left: 10rpx;
	}
	
	/* 食物列表 */
	.food-list {
		margin: 0 20rpx 20rpx 20rpx;
	}

	.food-item {
		background-color: #ffffff;
		padding: 30rpx;
		border-radius: 16rpx;
		margin-bottom: 20rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
	}
	
	.food-info {
		display: flex;
		align-items: center;
		flex: 1;
	}
	
	.food-icon {
		width: 20rpx;
		height: 20rpx;
		border-radius: 50%;
		margin-right: 20rpx;
	}
	
	.food-icon.orange {
		background-color: #FF9500;
	}
	
	.food-icon.green {
		background-color: #00D4AA;
	}
	
	.food-details {
		flex: 1;
	}
	
	.food-name {
		font-size: 32rpx;
		color: #333333;
		font-weight: bold;
		display: block;
		margin-bottom: 10rpx;
	}
	
	.food-amount {
		font-size: 24rpx;
		color: #666666;
		display: block;
		margin-bottom: 10rpx;
	}
	
	.food-nutrition {
		display: flex;
		gap: 20rpx;
	}
	
	.nutrition-item {
		font-size: 22rpx;
		color: #999999;
	}
	
	.food-calorie {
		display: flex;
		align-items: center;
	}
	
	.calorie-text {
		font-size: 28rpx;
		color: #333333;
		font-weight: bold;
		margin-right: 10rpx;
	}
	
	.arrow-icon {
		width: 20rpx;
		height: 20rpx;
	}
</style>
